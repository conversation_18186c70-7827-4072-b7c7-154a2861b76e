{"compilerOptions": {"rootDir": ".", "paths": {"react-native-wini-mobile-components": ["./src/index"]}, "allowUnreachableCode": false, "allowUnusedLabels": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "lib": ["ESNext"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "resolvePackageJsonImports": false, "skipLibCheck": true, "strict": true, "target": "ESNext", "verbatimModuleSyntax": false}, "include": ["src/**/*"]}