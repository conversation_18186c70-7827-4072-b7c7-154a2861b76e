# react-native-wini-mobile-components

wini-mobile-components

## Installation

```sh
npm install react-native-wini-mobile-components
```

## Usage

```js
import { multiply } from 'react-native-wini-mobile-components';

// ...

const result = await multiply(3, 7);
```

## Contributing

See the [contributing guide](CONTRIBUTING.md) to learn how to contribute to the repository and the development workflow.

## License

MIT

---

Made with [create-react-native-library](https://github.com/callstack/react-native-builder-bob)
