{"name": "wini-mobile-components", "version": "1.0.53", "description": "wini-mobile-components", "source": "./src/index.tsx", "main": "./lib/commonjs/index.js", "private": false, "module": "./lib/module/index.js", "types": "lib/typescript/module/src/index.d.ts", "sideEffects": false, "exports": {".": {"import": {"types": "./lib/typescript/module/src/index.d.ts", "default": "./lib/module/index.js"}, "require": {"types": "./lib/typescript/commonjs/src/index.d.ts", "default": "./lib/commonjs/index.js"}}}, "files": ["src", "lib", "*.podsp<PERSON>", "react-native.config.js"], "scripts": {"example": "npm run workspace wini-mobile-components-example", "test": "jest", "typecheck": "tsc", "lint": "eslint \"**/*.{js,ts,tsx}\"", "clean": "<PERSON><PERSON><PERSON> lib", "prepare": "bob build", "prettier": "npx prettier --write .", "build": "bob build", "release": "bob build && npm pack && npm publish"}, "keywords": ["react-native", "ios", "android"], "repository": {"type": "git", "url": "git+https://github.com/FDITECH/wini-mobile-modules.git"}, "author": "chienv <<EMAIL>> (https://github.com/liemnt94)", "license": "MIT", "bugs": {"url": "https://github.com/FDITECH/wini-mobile-modules/issues"}, "homepage": "https://github.com/FDITECH/wini-mobile-modules#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@commitlint/config-conventional": "^17.0.2", "@evilmartians/lefthook": "^1.5.0", "@react-native/eslint-config": "^0.73.1", "@release-it/conventional-changelog": "^9.0.2", "@types/color": "^4.2.0", "@types/jest": "^29.5.5", "@types/react": "^18.2.44", "commitlint": "^17.0.2", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.0.3", "react": "18.3.1", "react-native": "0.76.7", "react-native-builder-bob": "^0.37.0", "release-it": "^17.10.0", "rimraf": "^5.0.0", "typescript": "^5.2.2"}, "peerDependencies": {"react": ">=17.0.0", "react-native": ">=0.64.0"}, "workspaces": ["example"], "packageManager": "npm@latest", "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "extends": ["@react-native", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json", "esm": true}]]}, "create-react-native-library": {"languages": "js", "type": "library", "version": "0.48.3"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-masked-view/masked-view": "^0.3.2", "axios": "^1.8.3", "date-fns": "^4.1.0", "react-native-animated-spinkit": "^1.5.2", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.13.1", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "^15.11.2", "react-hook-form": "^7.53.2"}}