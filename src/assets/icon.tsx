import { SvgXml } from 'react-native-svg';

export const OutlinePLus = ({
  size,
  color,
}: {
  size?: number;
  color?: string;
}) => (
  <SvgXml
    xml={`<svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.5625 4.75H17.4375C17.6042 4.75 17.6875 4.83333 17.6875 5V27C17.6875 27.1667 17.6042 27.25 17.4375 27.25H15.5625C15.3958 27.25 15.3125 27.1667 15.3125 27V5C15.3125 4.83333 15.3958 4.75 15.5625 4.75Z" fill="${color ?? '#667994'}"/>
<path d="M6 14.8125H27C27.1667 14.8125 27.25 14.8958 27.25 15.0625V16.9375C27.25 17.1042 27.1667 17.1875 27 17.1875H6C5.83333 17.1875 5.75 17.1042 5.75 16.9375V15.0625C5.75 14.8958 5.83333 14.8125 6 14.8125Z" fill="${color ?? '#667994'}"/></svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

export const FilledUser = ({
  size,
  color,
}: {
  size?: number;
  color?: string;
}) => (
  <SvgXml
    xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.308 11.4047C11.6167 11.8276 10.8393 12.0832 10.0007 12.0832C9.16198 12.0832 8.38465 11.8276 7.69332 11.4047C5.26332 11.5707 3.33398 13.6776 3.33398 16.2498V17.4512L3.79732 17.6061C3.88665 17.6353 6.02598 18.3332 10.0007 18.3332C13.9753 18.3332 16.1147 17.6353 16.204 17.6061L16.6673 17.4512V16.2498C16.6673 13.6776 14.738 11.5707 12.308 11.4047Z" fill="${color ?? '#667994'}" opacity="${1}"/>
<path d="M10.0007 10.6943C12.2533 10.6943 14.0007 8.08109 14.0007 5.83317C14.0007 3.53525 12.2067 1.6665 10.0007 1.6665C7.79465 1.6665 6.00065 3.53525 6.00065 5.83317C6.00065 8.08109 7.74798 10.6943 10.0007 10.6943Z" fill="${color ?? '#667994'}" opacity="${1}"/>
</svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

export const OutlineUser = ({
  size,
  color,
}: {
  size?: number;
  color?: string;
}) => (
  <SvgXml
    xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.33398 16.2498C3.33398 13.5649 5.42313 11.3887 8.00065 11.3887H12.0007C14.5782 11.3887 16.6673 13.5649 16.6673 16.2498V17.4512L16.2038 17.6057L16.0007 16.9443C16.2038 17.6057 16.2038 17.6057 16.2038 17.6057L16.2021 17.6063L16.1996 17.6071L16.1923 17.6095L16.1685 17.617C16.1486 17.6233 16.1206 17.6319 16.0845 17.6425C16.0123 17.6637 15.9079 17.6929 15.7718 17.7275C15.4994 17.7966 15.1002 17.887 14.5779 17.9767C13.5333 18.1562 11.997 18.3332 10.0007 18.3332C8.00435 18.3332 6.46805 18.1562 5.4234 17.9767C4.90113 17.887 4.50186 17.7966 4.22955 17.7275C4.0934 17.6929 3.98898 17.6637 3.91681 17.6425C3.88072 17.6319 3.85269 17.6233 3.83278 17.617L3.80901 17.6095L3.80171 17.6071L3.7992 17.6063L3.79824 17.6059C3.79824 17.6059 3.79747 17.6057 4.00065 16.9443L3.79747 17.6057L3.33398 17.4512V16.2498ZM4.66732 16.4083C4.90346 16.4652 5.22859 16.5356 5.6404 16.6063C6.61659 16.774 8.08029 16.9443 10.0007 16.9443C11.921 16.9443 13.3847 16.774 14.3609 16.6063C14.7727 16.5356 15.0978 16.4652 15.334 16.4083V16.2498C15.334 14.332 13.8418 12.7776 12.0007 12.7776H8.00065C6.15951 12.7776 4.66732 14.332 4.66732 16.2498V16.4083Z" fill="${color ?? '#667994'}" opacity="${1}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0007 3.05539C8.52817 3.05539 7.33398 4.29934 7.33398 5.83317C7.33398 6.61913 7.6463 7.51296 8.1685 8.20721C8.69271 8.90413 9.35133 9.30539 10.0007 9.30539C10.65 9.30539 11.3086 8.90413 11.8328 8.20721C12.355 7.51296 12.6673 6.61913 12.6673 5.83317C12.6673 4.29934 11.4731 3.05539 10.0007 3.05539ZM6.00065 5.83317C6.00065 3.53228 7.79179 1.6665 10.0007 1.6665C12.2095 1.6665 14.0007 3.53228 14.0007 5.83317C14.0007 6.96458 13.5666 8.15408 12.8823 9.06382C12.2 9.97089 11.192 10.6943 10.0007 10.6943C8.8093 10.6943 7.80125 9.97089 7.11896 9.06382C6.43467 8.15408 6.00065 6.96458 6.00065 5.83317Z" fill="${color ?? '#667994'}" opacity="${1}"/>
</svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

export const OutlineUserProfile = ({
  size,
  color,
}: {
  size?: number;
  color?: string;
}) => (
  <SvgXml
    xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.74963 13.1359C3.75531 13.1408 3.7611 13.1456 3.76698 13.1502C4.91836 14.0978 6.39306 14.6668 8.00065 14.6668C9.60696 14.6668 11.0806 14.0987 12.2316 13.1525L12.2345 13.1502C12.2412 13.1449 12.2478 13.1394 12.2543 13.1337C13.7284 11.9109 14.6673 10.0652 14.6673 8.00016C14.6673 4.31826 11.6825 1.3335 8.00065 1.3335C4.31875 1.3335 1.33398 4.31826 1.33398 8.00016C1.33398 10.0664 2.27396 11.913 3.74963 13.1359ZM2.4451 8.00016C2.4451 4.93191 4.9324 2.44461 8.00065 2.44461C11.0689 2.44461 13.5562 4.93191 13.5562 8.00016C13.5562 9.33434 13.0859 10.5587 12.302 11.5164C12.1222 11.0401 11.7811 10.6317 11.325 10.3711L10.1837 9.718C10.5562 9.24529 10.7784 8.64867 10.7784 8.00016V6.88905C10.7784 5.355 9.5347 4.11127 8.00065 4.11127C6.4666 4.11127 5.22287 5.355 5.22287 6.88905V8.00016C5.22287 8.64815 5.44479 9.24434 5.81676 9.71687L4.67577 10.3713C4.21982 10.6319 3.87885 11.0401 3.69917 11.5163C2.91536 10.5586 2.4451 9.33429 2.4451 8.00016ZM11.334 12.445C10.4055 13.1425 9.25132 13.5557 8.00065 13.5557C6.74998 13.5557 5.59583 13.1424 4.66732 12.445V12.3007C4.66732 11.9017 4.88102 11.5336 5.22728 11.3359L6.73282 10.4724C7.11292 10.6677 7.54392 10.7779 8.00065 10.7779C8.4566 10.7779 8.8869 10.6681 9.26652 10.4734L10.774 11.336C11.1203 11.5337 11.334 11.9018 11.334 12.3008V12.445ZM8.00065 5.22238C7.08025 5.22238 6.33398 5.96865 6.33398 6.88905V8.00016C6.33398 8.5676 6.61763 9.06885 7.05085 9.36985L7.05746 9.37441C7.32567 9.55886 7.65057 9.66683 8.00065 9.66683C8.34596 9.66683 8.66677 9.56179 8.93285 9.38191C8.94116 9.37583 8.94963 9.36999 8.95824 9.3644C9.38707 9.06282 9.66732 8.56419 9.66732 8.00016V6.88905C9.66732 5.96865 8.92105 5.22238 8.00065 5.22238Z" fill="${color ?? '#667994'}" opacity="${1}"/>
</svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

export const OutlineStar = ({
  size,
  color,
}: {
  size?: number;
  color?: string;
}) => (
  <SvgXml
    xml={`<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.00065 0.333496C7.23136 0.333496 7.44205 0.46448 7.54414 0.671368L9.27586 4.18075L13.1484 4.74342C13.3767 4.77659 13.5663 4.93649 13.6376 5.15589C13.7089 5.37529 13.6495 5.61613 13.4843 5.77716L10.6821 8.5087L11.3434 12.3656C11.3824 12.593 11.2889 12.8228 11.1023 12.9584C10.9156 13.0939 10.6682 13.1118 10.464 13.0044L7.00065 11.1831L3.5373 13.0044C3.33311 13.1118 3.08567 13.0939 2.89902 12.9584C2.71236 12.8228 2.61888 12.593 2.65786 12.3656L3.31917 8.5087L0.517016 5.77716C0.351825 5.61613 0.292377 5.37529 0.363668 5.15589C0.434959 4.93649 0.624624 4.77659 0.852918 4.74342L4.72544 4.18075L6.45716 0.671368C6.55925 0.46448 6.76995 0.333496 7.00065 0.333496ZM7.00065 2.30915L5.67142 5.00288C5.58314 5.18178 5.41249 5.30577 5.21507 5.33446L2.24244 5.76637L4.3934 7.86313C4.53623 8.00236 4.60141 8.20294 4.5677 8.39953L4.0601 11.36L6.71856 9.96191C6.89516 9.86904 7.10615 9.86904 7.28274 9.96191L9.94121 11.36L9.4336 8.39953C9.3999 8.20294 9.46508 8.00236 9.6079 7.86313L11.7589 5.76637L8.78623 5.33446C8.58881 5.30577 8.41816 5.18178 8.32988 5.00288L7.00065 2.30915Z" fill="${color ?? '#667994'}" opacity="${1}"/>
</svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);
