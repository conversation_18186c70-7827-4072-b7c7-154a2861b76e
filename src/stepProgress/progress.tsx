import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';

interface ProgressViewProps {
  progress: number; // Progress value between 0 and 1
  height?: number; // Optional height for the progress bar
  backgroundColor?: string; // Optional background color for the progress bar
  progressColor?: string; // Optional color for the progress indicator
  style?: ViewStyle;
}

const ProgressSlider: React.FC<ProgressViewProps> = ({
  progress,
  height = 8,
  backgroundColor = '#E5E5E5', // Default background color
  progressColor = '#4285F4', // Default progress color
  style,
}) => {
  return (
    <View style={[styles.container, { height, backgroundColor }, style]}>
      <View
        style={[
          styles.progressBar,
          { width: `${progress * 100}%`, backgroundColor: progressColor },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
});

export default ProgressSlider;
